// 交易相关的类型定义

export interface KlineData {
  openTime: number;
  open: string;
  high: string;
  low: string;
  close: string;
  volume: string;
  closeTime: number;
  quoteAssetVolume: string;
  numberOfTrades: number;
  takerBuyBaseAssetVolume: string;
  takerBuyQuoteAssetVolume: string;
}

export interface ProcessedKlineData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  change: number;
  changePercent: number;
}

export interface MarketData {
  symbol: string;
  monthly: ProcessedKlineData[]; // 36个月数据
  daily: ProcessedKlineData[]; // 30天数据
  hourly: ProcessedKlineData[]; // 168小时数据（7天）
  thirtyMin: ProcessedKlineData[]; // 48个30分钟数据（1天）
  oneMin: ProcessedKlineData[]; // 60个1分钟数据（1小时）
}

export interface TechnicalIndicators {
  rsi: number;
  macd: {
    macd: number;
    signal: number;
    histogram: number;
  };
  bollinger: {
    upper: number;
    middle: number;
    lower: number;
  };
  ema: {
    ema20: number;
    ema50: number;
    ema200: number;
  };
  support: number;
  resistance: number;
}

export interface TradingSignal {
  direction: "LONG" | "SHORT" | "HOLD";
  confidence: number; // 0-100
  entryPrice: number;
  stopLoss: number;
  takeProfit: number[];
  positionSize: number; // 建议仓位大小（百分比）
  leverage: number; // 建议杠杆倍数
  reasoning: string;
}

export interface RiskManagement {
  maxDrawdown: number;
  riskReward: number;
  winRate: number;
  expectedReturn: number;
}

export interface AIAnalysisResult {
  symbol: string;
  timestamp: number;
  marketTrend: {
    shortTerm: "BULLISH" | "BEARISH" | "NEUTRAL";
    mediumTerm: "BULLISH" | "BEARISH" | "NEUTRAL";
    longTerm: "BULLISH" | "BEARISH" | "NEUTRAL";
  };
  technicalIndicators: TechnicalIndicators;
  tradingSignal: TradingSignal;
  riskManagement: RiskManagement;
  marketCondition: {
    volatility: "HIGH" | "MEDIUM" | "LOW";
    volume: "HIGH" | "MEDIUM" | "LOW";
    momentum: "STRONG" | "WEAK" | "NEUTRAL";
  };
  keyLevels: {
    support: number[];
    resistance: number[];
  };
  aiInsights: string;
  warnings: string[];
}

export interface TradingAdvice {
  action: "BUY" | "SELL" | "HOLD";
  entryPrice: number;
  quantity: number;
  stopLoss: number;
  takeProfit: number[];
  timeframe: string;
  confidence: number;
  reasoning: string;
  riskLevel: "LOW" | "MEDIUM" | "HIGH";
}

export interface PositionManagement {
  initialPosition: number;
  addPositions: {
    price: number;
    size: number;
    condition: string;
  }[];
  exitStrategy: {
    partialExits: {
      price: number;
      percentage: number;
    }[];
    stopLoss: number;
    trailingStop: boolean;
  };
}

export interface MarketAnalysisRequest {
  symbol: string;
  timeframes: ("1M" | "1d" | "1h" | "30m" | "1m")[];
  includeIndicators: boolean;
  riskTolerance: "LOW" | "MEDIUM" | "HIGH";
}

// OpenAI API 配置相关类型
export interface OpenAIConfig {
  apiKey: string;
  baseURL: string;
  model: string;
}

export interface ConfigFormData {
  apiKey: string;
  baseURL: string;
  model: string;
}

export interface ConfigTestResult {
  success: boolean;
  message: string;
  latency?: number;
}

export interface CoinInfo {
  symbol: string;
  name: string;
  price: number;
  change24h: number;
  volume24h: number;
  marketCap: number;
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 滚仓策略相关类型
export interface RollingStrategy {
  basePosition: number; // 基础仓位百分比
  additionThreshold: number; // 加仓阈值
  maxPosition: number; // 最大仓位
  profitTarget: number; // 盈利目标
  stopLoss: number; // 止损点
  trendConfirmation: boolean; // 趋势确认
}

export interface TrendAnalysis {
  direction: "UP" | "DOWN" | "SIDEWAYS";
  strength: number; // 1-10
  duration: number; // 持续时间（小时）
  breakoutPotential: number; // 突破潜力 0-100
  volumeConfirmation: boolean;
}
