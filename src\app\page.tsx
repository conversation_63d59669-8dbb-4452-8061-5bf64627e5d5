import Image from "next/image";
import Link from "next/link";
import { TrendingUp, Bar<PERSON>hart3, Target, Shield } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-8 w-8 text-blue-500" />
              <span className="text-xl font-bold">AI交易分析</span>
            </div>
            <Link href="/trading">
              <Button>开始分析</Button>
            </Link>
          </div>
        </div>
      </nav>

      {/* 主要内容 */}
      <main className="container mx-auto px-4 py-16">
        {/* 英雄区域 */}
        <div className="text-center space-y-6 mb-16">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900">
            AI驱动的
            <span className="text-blue-500"> 加密货币</span>
            <br />
            交易分析平台
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            基于滚仓策略的智能交易分析，结合多时间维度数据和AI洞察，
            帮助您在加密货币市场中做出更明智的交易决策
          </p>
          <div className="flex gap-4 justify-center">
            <Link href="/trading">
              <Button size="lg" className="text-lg px-8 py-3">
                <TrendingUp className="mr-2 h-5 w-5" />
                立即开始分析
              </Button>
            </Link>
            <Button variant="outline" size="lg" className="text-lg px-8 py-3">
              了解更多
            </Button>
          </div>
        </div>

        {/* 功能特性 */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          <Card>
            <CardHeader>
              <BarChart3 className="h-8 w-8 text-blue-500 mb-2" />
              <CardTitle>多维度分析</CardTitle>
              <CardDescription>
                36个月、30天、7天、24小时、1小时多时间维度K线数据分析
              </CardDescription>
            </CardHeader>
          </Card>

          <Card>
            <CardHeader>
              <TrendingUp className="h-8 w-8 text-green-500 mb-2" />
              <CardTitle>滚仓策略</CardTitle>
              <CardDescription>
                基于专业滚仓策略，复利增长而非复利爆仓的交易理念
              </CardDescription>
            </CardHeader>
          </Card>

          <Card>
            <CardHeader>
              <Target className="h-8 w-8 text-purple-500 mb-2" />
              <CardTitle>精准建议</CardTitle>
              <CardDescription>
                AI分析提供具体的买入/卖出价位、止损止盈和仓位管理建议
              </CardDescription>
            </CardHeader>
          </Card>

          <Card>
            <CardHeader>
              <Shield className="h-8 w-8 text-red-500 mb-2" />
              <CardTitle>风险控制</CardTitle>
              <CardDescription>
                严格的风险管理机制，保护利润，控制回撤，避免重大损失
              </CardDescription>
            </CardHeader>
          </Card>
        </div>

        {/* 滚仓策略核心原则 */}
        <div className="bg-white rounded-lg shadow-lg p-8 mb-16">
          <h2 className="text-3xl font-bold text-center mb-8">
            滚仓策略核心原则
          </h2>
          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-semibold mb-4 text-green-600">
                ✅ 正确做法
              </h3>
              <ul className="space-y-2 text-gray-700">
                <li>• 只在趋势确立时加仓</li>
                <li>• 每次加仓都是独立决策</li>
                <li>• 严格回撤止损</li>
                <li>• 3-5倍底仓+浮动加仓策略</li>
                <li>• 盈利保护止损</li>
                <li>• 阶梯式减仓</li>
              </ul>
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-4 text-red-600">
                ❌ 错误做法
              </h3>
              <ul className="space-y-2 text-gray-700">
                <li>• 在震荡市中盲目加仓</li>
                <li>• 冲动梭哈全部资金</li>
                <li>• 不设置止损保护</li>
                <li>• 让盈利变成亏损</li>
                <li>• 贪婪不知止盈</li>
                <li>• 情绪化交易决策</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 开始使用 */}
        <div className="text-center bg-blue-500 text-white rounded-lg p-8">
          <h2 className="text-3xl font-bold mb-4">准备开始智能交易分析？</h2>
          <p className="text-xl mb-6">
            选择您感兴趣的加密货币，让AI为您提供专业的交易建议
          </p>
          <Link href="/trading">
            <Button size="lg" variant="secondary" className="text-lg px-8 py-3">
              <TrendingUp className="mr-2 h-5 w-5" />
              开始分析
            </Button>
          </Link>
        </div>
      </main>

      {/* 页脚 */}
      <footer className="bg-gray-900 text-white py-8">
        <div className="container mx-auto px-4 text-center">
          <p className="text-gray-400">
            © 2024 AI交易分析平台. 仅供学习和研究使用，不构成投资建议。
          </p>
        </div>
      </footer>
    </div>
  );
}
