import OpenAI from "openai";
import {
  MarketData,
  AIAnalysisResult,
  TradingSignal,
  TechnicalIndicators,
  RiskManagement,
  TrendAnalysis,
  RollingStrategy,
} from "@/types/trading";

export class AIAnalysisService {
  private openai: OpenAI | null = null;
  private static instance: AIAnalysisService;

  constructor() {
    if (process.env.OPENAI_API_KEY) {
      this.openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
      });
    }
  }

  static getInstance(): AIAnalysisService {
    if (!AIAnalysisService.instance) {
      AIAnalysisService.instance = new AIAnalysisService();
    }
    return AIAnalysisService.instance;
  }

  /**
   * 生成AI分析系统提示词
   */
  private generateSystemPrompt(): string {
    return `你是一位专业的加密货币交易分析师，专精于滚仓策略和风险管理。你的分析基于以下核心原则：

## 滚仓策略核心原则：
1. **复利增长，不是复利爆仓** - 严格控制风险，保护利润
2. **只在趋势确立时加仓** - 避免在震荡市中盲目操作
3. **每次加仓都是独立决策** - 不冲动，不梭哈
4. **严格回撤止损** - 盈利必须保护，绝不倒亏

## 交易铁律：
✅ 趋势确认后才加仓（突破回踩不破 + 成交量确认）
✅ 基础仓位5%，每突破关键位加3%
✅ 止损随盈利上移，锁死利润
✅ 3-5倍底仓+浮动加仓策略
✅ 盈利保护止损（赚到的钱绝不让市场抢回去）
✅ 阶梯式减仓（行情尾声逐步落袋为安）

## 风险控制：
- 最大单次风险不超过账户的2%
- 总持仓不超过账户的20%
- 严格执行止损，绝不抱有侥幸心理
- 市场不会奖励贪心，但一定会惩罚贪婪

## 分析要求：
1. 基于多时间维度数据进行综合分析
2. 识别趋势方向和强度
3. 确定关键支撑阻力位
4. 评估市场情绪和动量
5. 提供具体的入场、止损、止盈建议
6. 给出仓位管理和加仓策略

请始终以数据为准，保持客观理性，优先考虑风险控制。`;
  }

  /**
   * 生成用户分析请求提示词
   */
  private generateAnalysisPrompt(
    marketData: MarketData,
    indicators: TechnicalIndicators
  ): string {
    const { symbol, monthly, daily, hourly, thirtyMin, oneMin } = marketData;

    // 获取最新价格和变化
    const latestPrice = oneMin[oneMin.length - 1]?.close || 0;
    const dailyChange = daily[daily.length - 1]?.changePercent || 0;
    const hourlyChange = hourly[hourly.length - 1]?.changePercent || 0;

    // 计算波动率
    const volatility = this.calculateVolatility(daily);
    const volume24h = daily[daily.length - 1]?.volume || 0;
    const avgVolume = daily.slice(-7).reduce((sum, d) => sum + d.volume, 0) / 7;

    return `请分析 ${symbol} 的交易机会：

## 市场数据概览：
- 当前价格: $${latestPrice.toFixed(4)}
- 24小时变化: ${dailyChange.toFixed(2)}%
- 1小时变化: ${hourlyChange.toFixed(2)}%
- 24小时成交量: ${volume24h.toFixed(0)}
- 7日平均成交量: ${avgVolume.toFixed(0)}
- 波动率: ${volatility.toFixed(2)}%

## 技术指标：
- RSI(14): ${indicators.rsi.toFixed(2)}
- MACD: ${indicators.macd.macd.toFixed(
      4
    )} (信号线: ${indicators.macd.signal.toFixed(4)})
- 布林带: 上轨 ${indicators.bollinger.upper.toFixed(
      4
    )}, 中轨 ${indicators.bollinger.middle.toFixed(
      4
    )}, 下轨 ${indicators.bollinger.lower.toFixed(4)}
- EMA20: ${indicators.ema.ema20.toFixed(4)}
- EMA50: ${indicators.ema.ema50.toFixed(4)}
- EMA200: ${indicators.ema.ema200.toFixed(4)}
- 支撑位: ${indicators.support.toFixed(4)}
- 阻力位: ${indicators.resistance.toFixed(4)}

## 多时间维度分析：
### 月线趋势 (36个月):
最近3个月变化: ${monthly
      .slice(-3)
      .map((m) => `${m.changePercent.toFixed(1)}%`)
      .join(", ")}

### 日线趋势 (30天):
最近7天变化: ${daily
      .slice(-7)
      .map((d) => `${d.changePercent.toFixed(1)}%`)
      .join(", ")}

### 小时线趋势 (7天):
最近24小时变化: ${hourly
      .slice(-24)
      .map((h) => `${h.changePercent.toFixed(1)}%`)
      .join(", ")}

### 30分钟线 (24小时):
最近12个30分钟变化: ${thirtyMin
      .slice(-12)
      .map((t) => `${t.changePercent.toFixed(1)}%`)
      .join(", ")}

### 1分钟线 (1小时):
最近30分钟变化: ${oneMin
      .slice(-30)
      .map((o) => `${o.changePercent.toFixed(1)}%`)
      .join(", ")}

请基于滚仓策略原则，提供以下分析：

1. **趋势判断**: 短期、中期、长期趋势方向和强度
2. **入场时机**: 是否满足趋势确立条件
3. **交易建议**: 买多/买空/观望，具体价位和仓位
4. **风险管理**: 止损位、止盈位、加仓条件
5. **市场状态**: 当前是否适合滚仓策略
6. **关键位分析**: 重要支撑阻力位
7. **风险提示**: 需要注意的风险点

请以JSON格式返回分析结果，包含所有必要的交易参数。`;
  }

  /**
   * 计算价格波动率
   */
  private calculateVolatility(data: any[]): number {
    if (data.length < 2) return 0;

    const returns = data
      .slice(1)
      .map((item, index) => Math.log(item.close / data[index].close));

    const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance =
      returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) /
      returns.length;

    return Math.sqrt(variance) * Math.sqrt(365) * 100; // 年化波动率
  }

  /**
   * 执行AI分析
   */
  async analyzeMarket(
    marketData: MarketData,
    indicators: TechnicalIndicators,
    riskTolerance: "LOW" | "MEDIUM" | "HIGH" = "MEDIUM"
  ): Promise<AIAnalysisResult> {
    try {
      const systemPrompt = this.generateSystemPrompt();
      const analysisPrompt = this.generateAnalysisPrompt(
        marketData,
        indicators
      );

      const completion = await this.openai.chat.completions.create({
        model: "gpt-4o",
        messages: [
          { role: "system", content: systemPrompt },
          { role: "user", content: analysisPrompt },
        ],
        temperature: 0.3,
        max_tokens: 2000,
        response_format: { type: "json_object" },
      });

      const aiResponse = completion.choices[0]?.message?.content;
      if (!aiResponse) {
        throw new Error("AI分析响应为空");
      }

      // 解析AI响应
      const parsedResponse = JSON.parse(aiResponse);

      // 构建标准化的分析结果
      const result: AIAnalysisResult = {
        symbol: marketData.symbol,
        timestamp: Date.now(),
        marketTrend: {
          shortTerm: parsedResponse.trends?.shortTerm || "NEUTRAL",
          mediumTerm: parsedResponse.trends?.mediumTerm || "NEUTRAL",
          longTerm: parsedResponse.trends?.longTerm || "NEUTRAL",
        },
        technicalIndicators: indicators,
        tradingSignal: {
          direction: parsedResponse.signal?.direction || "HOLD",
          confidence: parsedResponse.signal?.confidence || 50,
          entryPrice:
            parsedResponse.signal?.entryPrice ||
            marketData.oneMin[marketData.oneMin.length - 1]?.close ||
            0,
          stopLoss: parsedResponse.signal?.stopLoss || 0,
          takeProfit: parsedResponse.signal?.takeProfit || [],
          positionSize: this.calculatePositionSize(
            riskTolerance,
            parsedResponse.signal?.confidence || 50
          ),
          leverage: parsedResponse.signal?.leverage || 1,
          reasoning:
            parsedResponse.signal?.reasoning || "基于技术分析的综合判断",
        },
        riskManagement: {
          maxDrawdown: parsedResponse.risk?.maxDrawdown || 5,
          riskReward: parsedResponse.risk?.riskReward || 2,
          winRate: parsedResponse.risk?.winRate || 60,
          expectedReturn: parsedResponse.risk?.expectedReturn || 0,
        },
        marketCondition: {
          volatility: this.classifyVolatility(
            this.calculateVolatility(marketData.daily)
          ),
          volume: this.classifyVolume(marketData.daily),
          momentum: parsedResponse.condition?.momentum || "NEUTRAL",
        },
        keyLevels: {
          support: parsedResponse.levels?.support || [indicators.support],
          resistance: parsedResponse.levels?.resistance || [
            indicators.resistance,
          ],
        },
        aiInsights: parsedResponse.insights || "基于当前市场数据的综合分析",
        warnings: parsedResponse.warnings || [],
      };

      return result;
    } catch (error) {
      console.error("AI分析失败:", error);
      throw new Error("AI分析服务暂时不可用，请稍后重试");
    }
  }

  /**
   * 计算建议仓位大小
   */
  private calculatePositionSize(
    riskTolerance: string,
    confidence: number
  ): number {
    const baseSize =
      {
        LOW: 3,
        MEDIUM: 5,
        HIGH: 8,
      }[riskTolerance] || 5;

    // 根据信心度调整仓位
    const confidenceMultiplier = confidence / 100;
    return Math.min(baseSize * confidenceMultiplier, 10); // 最大10%
  }

  /**
   * 分类波动率
   */
  private classifyVolatility(volatility: number): "HIGH" | "MEDIUM" | "LOW" {
    if (volatility > 80) return "HIGH";
    if (volatility > 40) return "MEDIUM";
    return "LOW";
  }

  /**
   * 分类成交量
   */
  private classifyVolume(dailyData: any[]): "HIGH" | "MEDIUM" | "LOW" {
    if (dailyData.length < 7) return "MEDIUM";

    const recent = dailyData[dailyData.length - 1]?.volume || 0;
    const average =
      dailyData.slice(-7).reduce((sum, d) => sum + d.volume, 0) / 7;

    const ratio = recent / average;
    if (ratio > 1.5) return "HIGH";
    if (ratio > 0.8) return "MEDIUM";
    return "LOW";
  }

  /**
   * 生成滚仓策略建议
   */
  async generateRollingStrategy(
    marketData: MarketData,
    analysisResult: AIAnalysisResult
  ): Promise<RollingStrategy> {
    const { tradingSignal, marketCondition } = analysisResult;

    // 基础仓位根据市场条件调整
    let basePosition = 5; // 默认5%
    if (marketCondition.volatility === "HIGH") basePosition = 3;
    if (marketCondition.volatility === "LOW") basePosition = 7;

    // 加仓阈值根据趋势强度调整
    const additionThreshold = tradingSignal.confidence > 80 ? 2 : 3;

    return {
      basePosition,
      additionThreshold,
      maxPosition: basePosition * 3, // 最大仓位为基础仓位的3倍
      profitTarget:
        tradingSignal.takeProfit[0] || tradingSignal.entryPrice * 1.05,
      stopLoss: tradingSignal.stopLoss,
      trendConfirmation: tradingSignal.confidence > 70,
    };
  }
}
