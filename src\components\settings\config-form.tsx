"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, CheckCircle, XCircle, Eye, EyeOff } from "lucide-react";
import { toast } from "sonner";
import { configService } from "@/lib/config-service";
import { ConfigFormData, ConfigTestResult } from "@/types/trading";

const configSchema = z.object({
  apiKey: z.string().min(1, "API Key 不能为空"),
  baseURL: z.string().url("请输入有效的 URL"),
  model: z.string().min(1, "请选择模型"),
});

export function ConfigForm() {
  const [isLoading, setIsLoading] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [testResult, setTestResult] = useState<ConfigTestResult | null>(null);
  const [showApiKey, setShowApiKey] = useState(false);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<ConfigFormData>({
    resolver: zodResolver(configSchema),
    defaultValues: {
      apiKey: "",
      baseURL: "https://api.openai.com/v1",
      model: "gpt-4o-mini",
    },
  });

  const watchedValues = watch();

  // 加载现有配置
  useEffect(() => {
    const config = configService.getConfig();
    setValue("apiKey", config.apiKey);
    setValue("baseURL", config.baseURL);
    setValue("model", config.model);
  }, [setValue]);

  // 保存配置
  const onSubmit = async (data: ConfigFormData) => {
    setIsLoading(true);
    try {
      configService.saveConfig(data);
      toast.success("配置保存成功");
      setTestResult(null);
    } catch (error: any) {
      toast.error("保存失败: " + error.message);
    } finally {
      setIsLoading(false);
    }
  };

  // 测试配置
  const handleTest = async () => {
    setIsTesting(true);
    setTestResult(null);
    
    try {
      const result = await configService.testConfig(watchedValues);
      setTestResult(result);
      
      if (result.success) {
        toast.success("配置测试成功");
      } else {
        toast.error("配置测试失败: " + result.message);
      }
    } catch (error: any) {
      setTestResult({
        success: false,
        message: "测试失败: " + error.message,
      });
      toast.error("测试失败: " + error.message);
    } finally {
      setIsTesting(false);
    }
  };

  // 重置配置
  const handleReset = () => {
    configService.clearConfig();
    setValue("apiKey", "");
    setValue("baseURL", "https://api.openai.com/v1");
    setValue("model", "gpt-4o-mini");
    setTestResult(null);
    toast.success("配置已重置");
  };

  const availableModels = configService.getAvailableModels();
  const commonBaseURLs = configService.getCommonBaseURLs();

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>OpenAI API 配置</CardTitle>
        <CardDescription>
          配置您的 OpenAI API 信息以启用 AI 分析功能
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* API Key */}
          <div className="space-y-2">
            <Label htmlFor="apiKey">API Key</Label>
            <div className="relative">
              <Input
                id="apiKey"
                type={showApiKey ? "text" : "password"}
                placeholder="sk-..."
                {...register("apiKey")}
                className="pr-10"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowApiKey(!showApiKey)}
              >
                {showApiKey ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
            {errors.apiKey && (
              <p className="text-sm text-red-500">{errors.apiKey.message}</p>
            )}
          </div>

          {/* Base URL */}
          <div className="space-y-2">
            <Label htmlFor="baseURL">Base URL</Label>
            <Select
              value={watchedValues.baseURL}
              onValueChange={(value) => setValue("baseURL", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择或输入 Base URL" />
              </SelectTrigger>
              <SelectContent>
                {commonBaseURLs.map((url) => (
                  <SelectItem key={url} value={url}>
                    {url}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Input
              id="baseURL"
              placeholder="或输入自定义 URL"
              {...register("baseURL")}
            />
            {errors.baseURL && (
              <p className="text-sm text-red-500">{errors.baseURL.message}</p>
            )}
          </div>

          {/* Model */}
          <div className="space-y-2">
            <Label htmlFor="model">模型</Label>
            <Select
              value={watchedValues.model}
              onValueChange={(value) => setValue("model", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择模型" />
              </SelectTrigger>
              <SelectContent>
                {availableModels.map((model) => (
                  <SelectItem key={model} value={model}>
                    {model}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.model && (
              <p className="text-sm text-red-500">{errors.model.message}</p>
            )}
          </div>

          {/* 测试结果 */}
          {testResult && (
            <Alert className={testResult.success ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}>
              <div className="flex items-center gap-2">
                {testResult.success ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-600" />
                )}
                <AlertDescription className={testResult.success ? "text-green-800" : "text-red-800"}>
                  {testResult.message}
                  {testResult.latency && ` (响应时间: ${testResult.latency}ms)`}
                </AlertDescription>
              </div>
            </Alert>
          )}

          {/* 按钮组 */}
          <div className="flex gap-3 pt-4">
            <Button type="submit" disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              保存配置
            </Button>
            
            <Button
              type="button"
              variant="outline"
              onClick={handleTest}
              disabled={isTesting || !watchedValues.apiKey}
            >
              {isTesting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              测试连接
            </Button>
            
            <Button
              type="button"
              variant="destructive"
              onClick={handleReset}
            >
              重置配置
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
