"use client";

import { Arrow<PERSON><PERSON><PERSON>, Settings } from "lucide-react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ConfigForm } from "@/components/settings/config-form";

export default function SettingsPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/">
                <Button variant="ghost" size="sm">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  返回首页
                </Button>
              </Link>
              <div className="flex items-center space-x-2">
                <Settings className="h-6 w-6 text-blue-500" />
                <span className="text-lg font-semibold">系统设置</span>
              </div>
            </div>
            <Link href="/trading">
              <Button>开始分析</Button>
            </Link>
          </div>
        </div>
      </nav>

      {/* 主要内容 */}
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto space-y-8">
          {/* 页面标题 */}
          <div className="text-center space-y-4">
            <h1 className="text-3xl font-bold text-gray-900">
              系统配置
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              配置您的 AI 服务参数，确保系统能够正常提供智能分析功能
            </p>
          </div>

          {/* 配置表单 */}
          <ConfigForm />

          {/* 使用说明 */}
          <div className="bg-white rounded-lg shadow-sm border p-6 space-y-4">
            <h2 className="text-xl font-semibold text-gray-900">使用说明</h2>
            <div className="space-y-3 text-gray-600">
              <div>
                <h3 className="font-medium text-gray-900">API Key</h3>
                <p className="text-sm">
                  您的 OpenAI API 密钥，通常以 "sk-" 开头。请确保密钥有足够的额度和权限。
                </p>
              </div>
              <div>
                <h3 className="font-medium text-gray-900">Base URL</h3>
                <p className="text-sm">
                  API 服务的基础地址。如果使用官方 OpenAI 服务，请保持默认值。
                  如果使用第三方代理或其他兼容服务，请修改为对应的地址。
                </p>
              </div>
              <div>
                <h3 className="font-medium text-gray-900">模型</h3>
                <p className="text-sm">
                  选择要使用的 AI 模型。推荐使用 gpt-4o-mini 以获得最佳的性价比，
                  或使用 gpt-4o 获得最高质量的分析结果。
                </p>
              </div>
            </div>
          </div>

          {/* 安全提示 */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h3 className="font-medium text-yellow-800 mb-2">安全提示</h3>
            <ul className="text-sm text-yellow-700 space-y-1">
              <li>• API Key 将保存在浏览器本地存储中，请确保设备安全</li>
              <li>• 不要在公共设备上保存敏感配置信息</li>
              <li>• 定期检查 API 使用情况，避免意外费用</li>
              <li>• 如果怀疑密钥泄露，请立即在 OpenAI 控制台重新生成</li>
            </ul>
          </div>
        </div>
      </main>
    </div>
  );
}
